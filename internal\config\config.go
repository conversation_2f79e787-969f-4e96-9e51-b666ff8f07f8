package config

import (
	"encoding/json"
	"fmt"
	"os"
)

type Config struct {
	MQTT *MQTT `json:"mqtt"`
}

type MQTT struct {
	Host         string `json:"host"`
	Port         int    `json:"port"`
	ClientID     string `json:"client_id"`
	Username     string `json:"username"`
	Password     string `json:"password"`
	topicSupport string `json:"topicSupport"`
	TLS          struct {
		Enable             bool `json:"enable"`
		InsecureSkipVerify bool `json:"insecure_skip_verify"`
	} `json:"tls"`
}

func Load(path string) (*Config, error) {
	b, err := os.ReadFile(path)
	if err != nil {
		// không coi là fatal — trả về config trống để HTTP vẫn chạy
		return &Config{MQTT: nil}, fmt.Errorf("read %s: %w", path, err)
	}
	var c Config
	if err := json.Unmarshal(b, &c); err != nil {
		return &Config{MQTT: nil}, fmt.<PERSON>rro<PERSON>("parse %s: %w", path, err)
	}
	return &c, nil
}
