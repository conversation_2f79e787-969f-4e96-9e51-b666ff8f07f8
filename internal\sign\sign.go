package sign

import (
	"crypto/ed25519"
	"crypto/x509"
	"encoding/base64"
	"encoding/pem"
	"fmt"
)

type Signer struct {
	sk ed25519.PrivateKey
}

func NewEd25519SignerFromPEM(pemStr string) (*Signer, error) {
	block, _ := pem.Decode([]byte(pemStr))
	if block == nil {
		return nil, fmt.<PERSON><PERSON><PERSON>("bad ed25519 private pem")
	}
	anyKey, err := x509.ParsePKCS8PrivateKey(block.Bytes)
	if err != nil {
		return nil, fmt.Errorf("parse ed25519 pkcs8: %w", err)
	}
	sk, ok := anyKey.(ed25519.PrivateKey)
	if !ok {
		return nil, fmt.Errorf("not ed25519 key")
	}
	return &Signer{sk: sk}, nil
}

func (s *Signer) SignB64(msg []byte) (string, error) {
	sig := ed25519.Sign(s.sk, msg)
	return base64.StdEncoding.EncodeToString(sig), nil
}

// ED25519 PRIVATE KEY (PKCS#8) — gi<PERSON> nh<PERSON> bản gốc (hãy thay bằng khóa thật của bạn)
const DefaultEd25519PrivPEM = `-----BEGIN PRIVATE KEY-----
MC4CAQAwBQYDK2VwBCIEIIi/bLivzl19/BxiA9D8bBgLdBv++NRS9TCHkkvRZEQc
-----END PRIVATE KEY-----
`
