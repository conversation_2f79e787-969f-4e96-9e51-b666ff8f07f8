/**
 * server.js (Node.js trong container)
 * - HTTPS server bật mTLS: bắt buộc client certificate (đã ký bởi CA của bạn).
 * - Nhận payload JSON: { uuid, ts, nonce, data_b64, signature }.
 * - Verify HMAC-SHA256 trên chuỗi canonical: `${uuid}|${ts}|${nonce}|${data_b64}`.
 * - Anti-replay: ts phải nằm trong "cửa sổ thời gian", nonce không được trùng (TTL).
 *
 * Chú ý:
 * - Dùng ENV để truyền đường dẫn certs và secret: CA_CERT, SERVER_CERT, SERVER_KEY, HMAC_SECRET.
 * - data_b64 là base64-encoded JSON bytes => không lo "thay đổi format JSON" khi verify HMAC.
 */

const fs = require('fs');
const https = require('https');
const express = require('express');
const crypto = require('crypto');

const app = express();

// Giới hạn body và parse JSON (mặc định)
app.use(express.json({ limit: '1mb' }));

// ----- Đọc cert + secret từ ENV hoặc đường dẫn mặc định -----
const CA_CERT = process.env.CA_CERT || '/certs/ca.crt';
const SERVER_CERT = process.env.SERVER_CERT || '/certs/server.crt';
const SERVER_KEY = process.env.SERVER_KEY || '/certs/server.key';
const HMAC_SECRET = (process.env.HMAC_SECRET || '').trim();

if (!HMAC_SECRET) {
  console.error('HMAC_SECRET chưa được set (ENV). Vui lòng set -e HMAC_SECRET=...');
  process.exit(1);
}

// ----- Tải chứng chỉ -----
const tlsOptions = {
  key: fs.readFileSync(SERVER_KEY),      // private key server
  cert: fs.readFileSync(SERVER_CERT),    // server cert
  ca: fs.readFileSync(CA_CERT),          // CA để verify client cert
  requestCert: true,                     // yêu cầu client gửi cert
  rejectUnauthorized: true,              // chỉ chấp nhận client cert hợp lệ (được CA ký)
  minVersion: 'TLSv1.2',
};

// ----- Anti-replay: check timestamp & nonce -----
const NONCE_TTL_MS = 5 * 60 * 1000;      // giữ nonce 5 phút
const TS_WINDOW_PAST_SEC = 30;           // chấp nhận quá khứ 30s
const TS_WINDOW_FUTURE_SEC = 10;         // cho lệch clock nhẹ 10s
const seenNonces = new Map();            // Map<nonce, Date>

function antiReplay(ts, nonce) {
  const nowSec = Math.floor(Date.now() / 1000);
  if (ts < nowSec - TS_WINDOW_PAST_SEC || ts > nowSec + TS_WINDOW_FUTURE_SEC) {
    return false;
  }
  const now = Date.now();
  // nếu nonce đã thấy và chưa hết TTL -> reject
  if (seenNonces.has(nonce) && (now - seenNonces.get(nonce)) < NONCE_TTL_MS) {
    return false;
  }
  seenNonces.set(nonce, now);
  return true;
}

// Dọn nonces cũ
setInterval(() => {
  const now = Date.now();
  for (const [n, t] of seenNonces.entries()) {
    if (now - t > NONCE_TTL_MS) seenNonces.delete(n);
  }
}, 60 * 1000);

// ----- Verify HMAC -----
function verifyHMAC({ uuid, ts, nonce, data_b64, signature }) {
  // canonical string để ký/verify (phải giống phía Go)
  const msg = `${uuid}|${ts}|${nonce}|${data_b64}`;
  const mac = crypto.createHmac('sha256', HMAC_SECRET);
  mac.update(msg);
  const expectedHex = mac.digest('hex');
  // so sánh an toàn
  return crypto.timingSafeEqual(Buffer.from(expectedHex, 'utf8'), Buffer.from(signature, 'utf8'));
}

// ----- Route nhận dữ liệu -----
app.post('/info', (req, res) => {
  // Kiểm tra mTLS: phải có client cert được verify
  if (!req.client.authorized) {
    return res.status(403).send('Client certificate required/invalid');
  }
  // (tuỳ chọn) xem CN từ client cert
  const cert = req.socket.getPeerCertificate();
  console.log('Client CN =', cert.subject && cert.subject.CN);

  const { uuid, ts, nonce, data_b64, signature } = req.body || {};
  if (!uuid || !ts || !nonce || !data_b64 || !signature) {
    return res.status(400).send('Missing fields');
  }

  if (!verifyHMAC({ uuid, ts, nonce, data_b64, signature })) {
    return res.status(401).send('Invalid signature');
  }

  if (!antiReplay(ts, nonce)) {
    return res.status(401).send('Replay or bad timestamp');
  }

  // Decode full system info
  let dataBytes, dataObj;
  try {
    dataBytes = Buffer.from(data_b64, 'base64');
    dataObj = JSON.parse(dataBytes.toString('utf8'));
  } catch (e) {
    return res.status(400).send('Invalid data_b64');
  }

  // 👉 Tại đây bạn làm check-hash / active-deactive theo UUID
  // Ví dụ demo: in ra hash để minh hoạ
  const hash = crypto.createHash('sha256').update(dataBytes).digest('hex').slice(0, 16);
  console.log(`ACCEPTED uuid=${uuid} ts=${ts} nonce=${nonce} data_hash=${hash}`);
  console.log('Full system data:', JSON.stringify(dataObj, null, 2));

  return res.status(200).send('ok');
});

// ----- Khởi tạo HTTPS server mTLS -----
https.createServer(tlsOptions, app).listen(8443, () => {
  console.log('mTLS HTTPS server listening on https://0.0.0.0:8443/info');
});
