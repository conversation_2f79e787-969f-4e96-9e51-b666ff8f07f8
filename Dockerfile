FROM node:18-alpine

# Cài ca-certificates để TLS validate chain
RUN apk add --no-cache ca-certificates

WORKDIR /app
COPY server.js /app/server.js

EXPOSE 8443
CMD [ "node", "server.js" ]



#########RUN CONTAINER
# # Build image
# docker build -t mtls-node-receiver .\container

# # Run container: mount certs và set HMAC_SECRET
# docker run --rm -p 8443:8443 `
#   -v ${PWD}\certs:/certs:ro `
#   -e HMAC_SECRET="mysecret123" `
#   mtls-node-receiver
