// main.go
package main

import (
	"bytes"
	"crypto/ed25519" // dùng để ký / verify chữ ký ed25519
	"crypto/tls"     // để chạy HTTPS (TLS)
	"crypto/x509"    // xử lý certificate, private key
	"encoding/base64"
	"encoding/json"
	"encoding/pem" // parse PEM block (private key nhúng)
	"fmt"
	"log"
	"net/http"
	"os/exec" // chạy PowerShell để lấy thông tin hệ thống Windows
	"strings"
	"time"
)

// ====== NHÚNG PEM (HÃY THAY BẰNG PEM THẬT CỦA BẠN) ======
const serverCertPEM = `-----BEGIN CERTIFICATE-----
MIICQTCCAeegAwIBAgIUM6jAOd+bxebp9UWJoM20eYYMsuMwCgYIKoZIzj0EAwIw
YDELMAkGA1UEBhMCVk4xDDAKBgNVBAgMA0hDTTEMMAoGA1UEBwwDSENNMRAwDgYD
VQQKDAdZb3VyT3JnMQ8wDQYDVQQLDAZTZXJ2ZXIxEjAQBgNVBAMMCWxvY2FsaG9z
dDAeFw0yNTA5MjUxMzAzMzdaFw0zMDA5MjQxMzAzMzdaMGAxCzAJBgNVBAYTAlZO
MQwwCgYDVQQIDANIQ00xDDAKBgNVBAcMA0hDTTEQMA4GA1UECgwHWW91ck9yZzEP
MA0GA1UECwwGU2VydmVyMRIwEAYDVQQDDAlsb2NhbGhvc3QwWTATBgcqhkjOPQIB
BggqhkjOPQMBBwNCAATt1ImV6Ua3TfgOdSnEkBJutwVYk92BoehQOKRVSwPc686s
t2WpDuvk5jbaYIV1jlR+8jzckGTS+/RA1Gna2zGFo38wfTAJBgNVHRMEAjAAMA4G
A1UdDwEB/wQEAwIFoDATBgNVHSUEDDAKBggrBgEFBQcDATAsBgNVHREEJTAjggls
b2NhbGhvc3SHBH8AAAGHEAAAAAAAAAAAAAAAAAAAAAEwHQYDVR0OBBYEFHqw3qYN
bSwMYs8gk7Uji6/ZzZoNMAoGCCqGSM49BAMCA0gAMEUCIQDJ3MiFBEfJOXYSlPNE
nsuUfp2Xa+nGvXBLUGWUpM8lyAIgNwJTm/rF8ckFS/mZ4dlgrG1atsVtB7Ng3yH8
SFAl5aU=
-----END CERTIFICATE-----`

const serverKeyPEM = `-----BEGIN EC PARAMETERS-----
BggqhkjOPQMBBw==
-----END EC PARAMETERS-----
**********************************************************************************************************************************************************************************************************************************`

// Ed25519 PRIVATE KEY (PKCS#8)
**********************************************************************************************************************************************
`

// =========================================================

// Cấu trúc lưu thông tin hệ thống Windows
type SysInfo struct {
	OS          string            `json:"os"`
	Model       string            `json:"model"`
	BIOSSerial  string            `json:"bios_serial"`
	UUID        string            `json:"uuid"`
	Serial      string            `json:"serial"`
	IsVirtual   bool              `json:"is_virtual"`
	DetectHints map[string]string `json:"detect_hints,omitempty"`
}

// Payload gửi về client, gồm UUID, timestamp, nonce, dữ liệu (base64) + chữ ký
type Payload struct {
	UUID      string `json:"uuid"`
	Ts        int64  `json:"ts"`
	Nonce     string `json:"nonce"`
	DataB64   string `json:"data_b64"`
	Alg       string `json:"alg"`       // "ed25519"
	Signature string `json:"signature"` // chữ ký base64
}

// NEW: dữ liệu tối giản gửi trong data_b64 để client nhận combo + trường lẻ
type Minimal struct { // NEW
	UUID   string `json:"uuid"` // để client kiểm tra khớp với p.uuid
	Serial string `json:"serial"`
	Model  string `json:"model"`
	Combo  string `json:"combo"` // Serial|Model|UUID
}

func main() {
	// Parse TLS cert+private key từ chuỗi PEM nhúng
	cert, err := tls.X509KeyPair([]byte(serverCertPEM), []byte(serverKeyPEM))
	if err != nil {
		log.Fatalf("load embedded TLS keypair: %v", err)
	}

	// Cấu hình TLS (chạy HTTPS)
	tlsCfg := &tls.Config{
		Certificates: []tls.Certificate{cert},
		MinVersion:   tls.VersionTLS12, // yêu cầu TLS >= 1.2
	}

	// Router HTTP
	mux := http.NewServeMux()

	// Endpoint /health: trả về "OK" để kiểm tra server sống
	mux.HandleFunc("/health", func(w http.ResponseWriter, r *http.Request) {
		w.Write([]byte("OK\n"))
	})

	// Endpoint /info: trả về thông tin hệ thống kèm chữ ký ed25519
	mux.HandleFunc("/info", func(w http.ResponseWriter, r *http.Request) {
		sys, err := collectWindowsInfo() // thu thập thông tin máy Windows
		if err != nil {
			http.Error(w, "collect info error: "+err.Error(), 500)
			return
		}

		// === CHANGED: tạo combo và thay sys.UUID thành combo ===
		rawUUID := strings.TrimSpace(sys.UUID) // lưu lại UUID gốc nếu cần
		combo := fmt.Sprintf("%s%s%s",
			strings.TrimSpace(sys.Serial),
			strings.TrimSpace(sys.Model),
			rawUUID,
		) // CHANGED

		// (tuỳ chọn) lưu UUID gốc vào DetectHints để không mất thông tin
		if sys.DetectHints == nil {
			sys.DetectHints = map[string]string{}
		}
		sys.DetectHints["RawUUID"] = rawUUID // CHANGED

		sys.UUID = combo // CHANGED: từ giờ uuid trong JSON là combo

		// Mã hoá sys (đã thay uuid thành combo) -> base64
		sysJSON, _ := json.Marshal(sys) // CHANGED
		dataB64 := base64.StdEncoding.EncodeToString(sysJSON)

		// Tạo timestamp (giây) và nonce (ngẫu nhiên theo nano giây)
		ts := time.Now().Unix()
		nonce := fmt.Sprintf("%d", time.Now().UnixNano())

		// CHANGED: ký trên chuỗi với uuid = combo
		msg := []byte(fmt.Sprintf("%s|%d|%s|%s", sys.UUID, ts, nonce, dataB64)) // CHANGED

		// Ký bằng private key Ed25519
		sigB64, err := signEd25519(msg)
		if err != nil {
			http.Error(w, "sign error: "+err.Error(), 500)
			return
		}

		// Gói payload trả về cho client (giữ nguyên schema)
		resp := &Payload{
			UUID:      sys.UUID, // CHANGED (combo)
			Ts:        ts,
			Nonce:     nonce,
			DataB64:   dataB64,
			Alg:       "ed25519",
			Signature: sigB64,
		}
		writeJSON(w, resp, 200)
	})

	// Tạo http.Server với log middleware
	srv := &http.Server{Handler: logMW(mux), TLSConfig: tlsCfg}

	// Listen TLS chỉ trên localhost:8443
	ln, err := tls.Listen("tcp", "127.0.0.1:8443", tlsCfg)
	if err != nil {
		log.Fatalf("listen tls: %v", err)
	}
	log.Printf("Go server on https://127.0.0.1:8443 (embedded TLS, ed25519 signing)")

	// Serve HTTPS
	if err := srv.Serve(ln); err != nil {
		log.Fatal(err)
	}
}

// Ghi JSON response ra HTTP response
func writeJSON(w http.ResponseWriter, v any, status int) {
	w.Header().Set("Content-Type", "application/json; charset=utf-8")
	w.WriteHeader(status)
	json.NewEncoder(w).Encode(v)
}

// Middleware log: ghi log mỗi request
func logMW(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		start := time.Now()
		rr := &respRec{ResponseWriter: w, status: 200}
		next.ServeHTTP(rr, r)
		log.Printf("%s %s %d %s", r.Method, r.URL.Path, rr.status, time.Since(start))
	})
}

// respRec để bắt status code response
type respRec struct {
	http.ResponseWriter
	status int
}

func (rr *respRec) WriteHeader(code int) { rr.status = code; rr.ResponseWriter.WriteHeader(code) }

// ====== Hàm ký Ed25519 ======
func signEd25519(msg []byte) (string, error) {
	// Decode PEM
	block, _ := pem.Decode([]byte(ed25519PrivPEM))
	if block == nil {
		return "", fmt.Errorf("bad ed25519 private pem")
	}
	// Parse private key PKCS#8
	keyAny, err := x509.ParsePKCS8PrivateKey(block.Bytes)
	if err != nil {
		return "", fmt.Errorf("parse ed25519 pkcs8: %w", err)
	}
	sk, ok := keyAny.(ed25519.PrivateKey)
	if !ok {
		return "", fmt.Errorf("not ed25519 key")
	}
	// Ký message
	sig := ed25519.Sign(sk, msg)
	// Trả về base64 chữ ký
	return base64.StdEncoding.EncodeToString(sig), nil
}

// ====== Thu thập thông tin Windows ======

// Hàm helper: chạy lệnh PowerShell và lấy stdout
func runPowershell(expr string) (string, error) {
	cmd := exec.Command("powershell", "-NoProfile", "-Command", expr)
	var buf bytes.Buffer
	cmd.Stdout, cmd.Stderr = &buf, &buf
	err := cmd.Run()
	out := strings.ReplaceAll(buf.String(), "\r\n", "\n")
	return strings.TrimSpace(out), err
}

// Thu thập thông tin hệ thống bằng WMI (qua PowerShell)
func collectWindowsInfo() (*SysInfo, error) {
	model, err1 := runPowershell("(Get-CimInstance Win32_ComputerSystem).Manufacturer + ' ' + (Get-CimInstance Win32_ComputerSystem).Model")
	biosSerial, err2 := runPowershell("(Get-CimInstance Win32_BIOS).SerialNumber")
	machineUUID, err3 := runPowershell("(Get-CimInstance Win32_ComputerSystemProduct).UUID")
	boardSerial, err4 := runPowershell("(Get-CimInstance Win32_BaseBoard).SerialNumber")

	// Nếu tất cả lệnh đều lỗi → không lấy được thông tin
	if err1 != nil && err2 != nil && err3 != nil && err4 != nil {
		return nil, fmt.Errorf("cannot collect any wmi info")
	}

	// Kiểm tra xem có chạy trong máy ảo không (theo tên model)
	low := strings.ToLower(model)
	isVirtual := strings.Contains(low, "vmware") || strings.Contains(low, "virtualbox") ||
		strings.Contains(low, "kvm") || strings.Contains(low, "qemu") ||
		strings.Contains(low, "hyper-v") || strings.Contains(low, "xen")

	// Trả về struct SysInfo
	return &SysInfo{
		OS:          "windows",
		Model:       strings.TrimSpace(model),
		BIOSSerial:  strings.TrimSpace(biosSerial),
		UUID:        strings.TrimSpace(machineUUID),
		Serial:      strings.TrimSpace(boardSerial),
		IsVirtual:   isVirtual,
		DetectHints: map[string]string{"ManufacturerModel": model},
	}, nil
}
