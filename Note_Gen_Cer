$ErrorActionPreference = "Stop"

# --- settings you can tweak ---
$DAYS_CA   = 3650
$DAYS_LEAF = 825
$ORG       = "/C=US/ST=State/L=City/O=MyOrg"
$SERVER_CN = "localhost"
$CLIENT_CN = "host"
# ------------------------------

New-Item -ItemType Directory -Force -Path certs | Out-Null
Set-Location certs

# 0) Minimal OpenSSL config so Windows builds don't choke
@"
# Minimal openssl.cnf
# Only what we need for 'req' and a basic CA
[ req ]
default_bits       = 2048
distinguished_name = dn
prompt             = no
string_mask        = utf8only

[ dn ]
C  = US
ST = State
L  = City
O  = MyOrg
OU = Generated
CN = placeholder

# Extensions for a self-signed CA cert
[ v3_ca ]
basicConstraints = critical, CA:true, pathlen:0
keyUsage         = critical, keyCertSign, cRLSign
subjectKeyIdentifier = hash
authorityKeyIdentifier = keyid:always,issuer
"@ | Out-File -Encoding ascii openssl.cnf

# 1) CA key (reuse if you already have ca.key)
if (-not (Test-Path ca.key)) {
  Write-Host "Generating CA key..."
  openssl genrsa -out ca.key 4096
}

# 2) CA cert (self-signed)
if (-not (Test-Path ca.crt)) {
  Write-Host "Generating CA cert..."
  openssl req -x509 -new -key ca.key -sha256 -days $DAYS_CA -out ca.crt `
    -subj "$ORG/OU=CA/CN=MyRootCA" -config .\openssl.cnf -extensions v3_ca
}

# 3) Server key (reuse existing)
if (-not (Test-Path server.key)) {
  Write-Host "Generating server key..."
  openssl genrsa -out server.key 2048
}

# 4) Server CSR
Write-Host "Generating server CSR..."
openssl req -new -key server.key -out server.csr `
  -subj "$ORG/OU=Servers/CN=$SERVER_CN" -config .\openssl.cnf

# 5) Server cert extensions (SAN + EKU)
@"
basicConstraints=CA:FALSE
keyUsage=critical, digitalSignature, keyEncipherment
extendedKeyUsage=serverAuth
subjectAltName=@alt_names
[alt_names]
DNS.1=localhost
IP.1=127.0.0.1
IP.2=::1
"@ | Out-File -Encoding ascii server-ext.cnf

# 6) Sign server certificate with CA
Write-Host "Signing server cert..."
openssl x509 -req -in server.csr -CA ca.crt -CAkey ca.key -CAcreateserial `
  -out server.crt -days $DAYS_LEAF -sha256 -extfile server-ext.cnf

# 7) Client (host) key (reuse existing)
if (-not (Test-Path host.key)) {
  Write-Host "Generating client (host) key..."
  openssl genrsa -out host.key 2048
}

# 8) Client (host) CSR
Write-Host "Generating client (host) CSR..."
openssl req -new -key host.key -out host.csr `
  -subj "$ORG/OU=Clients/CN=$CLIENT_CN" -config .\openssl.cnf

# 9) Client (host) cert extensions (clientAuth EKU)
@"
basicConstraints=CA:FALSE
keyUsage=critical, digitalSignature, keyEncipherment
extendedKeyUsage=clientAuth
subjectAltName=DNS:host
"@ | Out-File -Encoding ascii host-ext.cnf

# 10) Sign client (host) certificate with CA
Write-Host "Signing client (host) cert..."
openssl x509 -req -in host.csr -CA ca.crt -CAkey ca.key `
  -out host.crt -days $DAYS_LEAF -sha256 -extfile host-ext.cnf

# 11) Optional HMAC secret (keep your existing one if present)
if (-not (Test-Path hmac_secret.txt)) {
  (openssl rand -hex 32) | Out-File -Encoding ascii hmac_secret.txt
}

# 12) Verify chain
Write-Host "Verifying chains..."
openssl verify -CAfile ca.crt server.crt
openssl verify -CAfile ca.crt host.crt

Write-Host "`nFiles in .\certs:"
Get-ChildItem -Force | Format-Table -Auto

Set-Location ..
Write-Host "`nDone. Use these in config.json (Windows paths):"
Write-Host '  "certs": {'
Write-Host '    "host_cert": "certs\host.crt",'
Write-Host '    "host_key":  "certs\host.key",'
Write-Host '    "ca_cert":   "certs\ca.crt"'
Write-Host '  },'
Write-Host '  "hmac": { "secret_file": "certs\hmac_secret.txt" }'
