package main

import (
	"bytes"
	"crypto/hmac"
	"crypto/sha256"
	"crypto/tls"
	"crypto/x509"
	"encoding/base64"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"time"
)

/*
<PERSON><PERSON><PERSON> năng (bản server):
- Chạy HTTPS server (tùy chọn mTLS). Client sẽ gọi tới để lấy thông tin.
- Tìm & nạp config.json:
    1) ENV CONFIG_PATH (nếu có)
    2) ./config.json tại CWD
    3) config.json cạnh .exe
- Đường dẫn tương đối trong config (certs, secret_file) sẽ resolve theo thư mục chứa config.json
- BẮT BUỘC lấy được hardware UUID (Win32_ComputerSystemProduct.UUID). Không có → thoát.
- Endpoints:
    GET /health → 200 OK (text)
    GET /raw    → trả SysInfo (JSON)
    GET /info   → tr<PERSON> Payload {uuid, ts, nonce, data_b64, signature}
*/

// ================== Cấu hình ==================

type Config struct {
	ListenAddr        string     `json:"listen_addr"`         // ví dụ ":8443"
	Certs             CertPaths  `json:"certs"`               // server cert/key + CA
	HMAC              HMACConfig `json:"hmac"`
	RequireClientCert bool       `json:"require_client_cert"` // true = mTLS
	// Back-compat fields (giữ để không lỗi khi bạn đã có config cũ)
	TargetURL string `json:"target_url,omitempty"`
	IntervalS string `json:"interval,omitempty"`
}

type CertPaths struct {
	// DÙNG LÀM SERVER CERT
	HostCert string `json:"host_cert"` // dùng làm server cert (giữ tên cũ để khỏi sửa file config)
	HostKey  string `json:"host_key"`  // dùng làm server key
	CACert   string `json:"ca_cert"`   // CA để verify client cert (nếu RequireClientCert=true)
}

type HMACConfig struct {
	Secret     string `json:"secret"`
	SecretFile string `json:"secret_file"`
}

// ================== Kiểu dữ liệu gửi ==================

type SysInfo struct {
	OS          string            `json:"os"`
	Model       string            `json:"model"`
	BIOSSerial  string            `json:"bios_serial"`
	UUID        string            `json:"uuid"`
	Serial      string            `json:"serial"`
	IsVirtual   bool              `json:"is_virtual"`
	DetectHints map[string]string `json:"detect_hints,omitempty"`
}

type Payload struct {
	UUID      string `json:"uuid"`
	Ts        int64  `json:"ts"`
	Nonce     string `json:"nonce"`
	DataB64   string `json:"data_b64"`
	Signature string `json:"signature"`
}

// ================== main ==================

func main() {
	exeDir := mustExeDir()

	// Tìm config
	cfgPath := findConfigPath(exeDir)
	var cfgDir string
	if cfgPath != "" {
		cfgDir = filepath.Dir(cfgPath)
	} else {
		if wd, err := os.Getwd(); err == nil {
			cfgDir = wd
		} else {
			cfgDir = exeDir
		}
		log.Printf("Cảnh báo: không tìm thấy config.json ở ENV CONFIG_PATH, CWD hoặc cạnh exe. Dùng default + ENV (nếu có).")
	}

	// Nạp config
	cfg, err := loadConfig(cfgPath)
	if err != nil {
		log.Fatalf("Không đọc được config (%s): %v", cfgPath, err)
	}
	applyEnvOverridesServer(&cfg)

	// Mặc định listen ":8443"
	if strings.TrimSpace(cfg.ListenAddr) == "" {
		cfg.ListenAddr = ":8443"
	}

	// HMAC secret
	secretBytes, err := resolveHMACSecret(&cfg, cfgDir)
	if err != nil || len(secretBytes) == 0 {
		log.Fatal("Không tìm thấy HMAC secret: set ENV HMAC_SECRET / HMAC_SECRET_FILE hoặc chỉnh config.json")
	}

	// Lấy UUID phần cứng ngay lúc khởi động
	sys0, err := collectWindowsInfo()
	if err != nil {
		log.Fatalf("Lỗi thu thập thông tin hệ thống ban đầu: %v", err)
	}
	hostUUID := strings.TrimSpace(sys0.UUID)
	if hostUUID == "" {
		log.Fatal("Không lấy được hardware UUID từ Win32_ComputerSystemProduct.UUID — dừng chương trình")
	}

	// TLS server
	serverCertFile := resolvePath(cfgDir, cfg.Certs.HostCert)
	serverKeyFile := resolvePath(cfgDir, cfg.Certs.HostKey)
	caCertFile := resolvePath(cfgDir, cfg.Certs.CACert)

	tlsCfg, err := newServerTLSConfig(serverCertFile, serverKeyFile, caCertFile, cfg.RequireClientCert)
	if err != nil {
		log.Fatalf("TLS config lỗi: %v", err)
	}

	mux := http.NewServeMux()

	// Health
	mux.HandleFunc("/health", func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "text/plain; charset=utf-8")
		io.WriteString(w, "OK\n")
	})

	// Raw sysinfo
	mux.HandleFunc("/raw", func(w http.ResponseWriter, r *http.Request) {
		sys, err := collectWindowsInfo()
		if err != nil {
			http.Error(w, "collect info error: "+err.Error(), http.StatusInternalServerError)
			return
		}
		writeJSON(w, sys, http.StatusOK)
	})

	// Signed payload
	mux.HandleFunc("/info", func(w http.ResponseWriter, r *http.Request) {
		sys, err := collectWindowsInfo()
		if err != nil {
			http.Error(w, "collect info error: "+err.Error(), http.StatusInternalServerError)
			return
		}
		sysJSON, _ := json.Marshal(sys)
		dataB64 := base64.StdEncoding.EncodeToString(sysJSON)

		ts := time.Now().Unix()
		nonce := fmt.Sprintf("%d", time.Now().UnixNano())
		sig := signHMAC(hostUUID, ts, nonce, dataB64, secretBytes)

		resp := &Payload{
			UUID:      hostUUID,
			Ts:        ts,
			Nonce:     nonce,
			DataB64:   dataB64,
			Signature: sig,
		}
		writeJSON(w, resp, http.StatusOK)
	})

	srv := &http.Server{
		Addr:      cfg.ListenAddr,
		Handler:   logMiddleware(mux),
		TLSConfig: tlsCfg,
	}

	log.Printf("Config: %s", firstNonEmpty(cfgPath, "(default)"))
	log.Printf("Listen: https://%s | mTLS: %v | HostUUID: %s", trimLeadingColon(cfg.ListenAddr), cfg.RequireClientCert, hostUUID)
	if cfg.RequireClientCert {
		log.Printf("Client CA: %s", caCertFile)
	}
	log.Printf("Server cert: %s | key: %s", serverCertFile, serverKeyFile)

	// Bắt đầu serve HTTPS
	if err := srv.ListenAndServeTLS("", ""); err != nil {
		log.Fatalf("ListenAndServeTLS: %v", err)
	}
}

// ================== Helpers: HTTP ==================

func writeJSON(w http.ResponseWriter, v any, status int) {
	w.Header().Set("Content-Type", "application/json; charset=utf-8")
	w.WriteHeader(status)
	enc := json.NewEncoder(w)
	enc.SetEscapeHTML(false)
	_ = enc.Encode(v)
}

func logMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		start := time.Now()
		rr := &respRecorder{ResponseWriter: w, status: 200}
		next.ServeHTTP(rr, r)
		log.Printf("%s %s %d %s", r.Method, r.URL.Path, rr.status, time.Since(start))
	})
}

type respRecorder struct {
	http.ResponseWriter
	status int
}

func (rr *respRecorder) WriteHeader(code int) {
	rr.status = code
	rr.ResponseWriter.WriteHeader(code)
}

// ================== Helpers: config & path ==================

func mustExeDir() string {
	p, err := os.Executable()
	if err != nil {
		return "."
	}
	return filepath.Dir(p)
}

func fileExists(p string) bool {
	if p == "" {
		return false
	}
	info, err := os.Stat(p)
	return err == nil && !info.IsDir()
}

func findConfigPath(exeDir string) string {
	if p := os.Getenv("CONFIG_PATH"); fileExists(p) {
		return p
	}
	if wd, err := os.Getwd(); err == nil {
		if p := filepath.Join(wd, "config.json"); fileExists(p) {
			return p
		}
	}
	if p := filepath.Join(exeDir, "config.json"); fileExists(p) {
		return p
	}
	return ""
}

func loadConfig(path string) (Config, error) {
	cfg := Config{
		ListenAddr: ":8443",
		Certs: CertPaths{
			HostCert: "certs\\server.crt", // dùng làm server cert
			HostKey:  "certs\\server.key", // dùng làm server key
			CACert:   "certs\\ca.crt",
		},
		HMAC: HMACConfig{
			Secret:     "",
			SecretFile: "certs\\hmac_secret.txt",
		},
		RequireClientCert: true,
	}
	if path == "" {
		return cfg, nil
	}
	data, err := os.ReadFile(path)
	if err != nil {
		log.Printf("Cảnh báo: không đọc được %s: %v. Dùng default + ENV nếu có.", path, err)
		return cfg, nil
	}
	if err := json.Unmarshal(data, &cfg); err != nil {
		return cfg, fmt.Errorf("config.json không hợp lệ: %w", err)
	}
	return cfg, nil
}

// ENV override (server)
func applyEnvOverridesServer(cfg *Config) {
	if v := os.Getenv("LISTEN_ADDR"); v != "" {
		cfg.ListenAddr = v
	}
	if v := os.Getenv("HOST_CERT"); v != "" {
		cfg.Certs.HostCert = v
	}
	if v := os.Getenv("HOST_KEY"); v != "" {
		cfg.Certs.HostKey = v
	}
	if v := os.Getenv("CA_CERT"); v != "" {
		cfg.Certs.CACert = v
	}
	if v := os.Getenv("REQUIRE_CLIENT_CERT"); v != "" {
		vv := strings.ToLower(strings.TrimSpace(v))
		cfg.RequireClientCert = vv == "1" || vv == "true" || vv == "yes"
	}
	if v := os.Getenv("HMAC_SECRET"); v != "" {
		cfg.HMAC.Secret = v
	}
	if v := os.Getenv("HMAC_SECRET_FILE"); v != "" {
		cfg.HMAC.SecretFile = v
	}
}

func resolvePath(baseDir, p string) string {
	if p == "" || filepath.IsAbs(p) {
		return p
	}
	return filepath.Join(baseDir, p)
}

func firstNonEmpty(a, b string) string {
	if a != "" {
		return a
	}
	return b
}

func trimLeadingColon(addr string) string {
	return strings.TrimPrefix(addr, ":")
}

// ================== Helpers: HMAC & TLS server ==================

func resolveHMACSecret(cfg *Config, cfgDir string) ([]byte, error) {
	if s := os.Getenv("HMAC_SECRET"); s != "" {
		return []byte(strings.TrimSpace(s)), nil
	}
	if f := os.Getenv("HMAC_SECRET_FILE"); f != "" {
		if b, err := os.ReadFile(f); err == nil {
			return bytes.TrimSpace(b), nil
		}
	}
	if cfg.HMAC.Secret != "" {
		return []byte(strings.TrimSpace(cfg.HMAC.Secret)), nil
	}
	if cfg.HMAC.SecretFile != "" {
		f := resolvePath(cfgDir, cfg.HMAC.SecretFile)
		if b, err := os.ReadFile(f); err == nil {
			return bytes.TrimSpace(b), nil
		}
	}
	return nil, fmt.Errorf("no secret")
}

func signHMAC(uuid string, ts int64, nonce string, dataB64 string, secret []byte) string {
	msg := fmt.Sprintf("%s|%d|%s|%s", uuid, ts, nonce, dataB64)
	m := hmac.New(sha256.New, secret)
	m.Write([]byte(msg))
	return hex.EncodeToString(m.Sum(nil))
}

func newServerTLSConfig(serverCertFile, serverKeyFile, caCertFile string, requireClientCert bool) (*tls.Config, error) {
	cert, err := tls.LoadX509KeyPair(serverCertFile, serverKeyFile)
	if err != nil {
		return nil, fmt.Errorf("load server cert/key: %w", err)
	}

	tlsCfg := &tls.Config{
		Certificates: []tls.Certificate{cert},
		MinVersion:   tls.VersionTLS12,
	}

	if requireClientCert {
		caPEM, err := os.ReadFile(caCertFile)
		if err != nil {
			return nil, fmt.Errorf("read ca cert (client verify): %w", err)
		}
		pool := x509.NewCertPool()
		if ok := pool.AppendCertsFromPEM(caPEM); !ok {
			return nil, fmt.Errorf("append ca failed")
		}
		tlsCfg.ClientCAs = pool
		tlsCfg.ClientAuth = tls.RequireAndVerifyClientCert
	} else {
		tlsCfg.ClientAuth = tls.NoClientCert
	}
	return tlsCfg, nil
}

// ================== Helpers: Thu thập info Windows ==================

func runPowershell(expr string) (string, error) {
	cmd := exec.Command("powershell", "-NoProfile", "-Command", expr)
	var buf bytes.Buffer
	cmd.Stdout = &buf
	cmd.Stderr = &buf
	err := cmd.Run()
	out := strings.ReplaceAll(buf.String(), "\r\n", "\n")
	return strings.TrimSpace(out), err
}

func collectWindowsInfo() (*SysInfo, error) {
	model, err1 := runPowershell("(Get-CimInstance Win32_ComputerSystem).Manufacturer + ' ' + (Get-CimInstance Win32_ComputerSystem).Model")
	biosSerial, err2 := runPowershell("(Get-CimInstance Win32_BIOS).SerialNumber")
	machineUUID, err3 := runPowershell("(Get-CimInstance Win32_ComputerSystemProduct).UUID")
	boardSerial, err4 := runPowershell("(Get-CimInstance Win32_BaseBoard).SerialNumber")

	if err1 != nil && err2 != nil && err3 != nil && err4 != nil {
		return nil, fmt.Errorf("không lấy được bất kỳ thông tin nào từ WMI/CIM")
	}

	isVirtual := false
	low := strings.ToLower(model)
	for _, v := range []string{"vmware", "virtualbox", "kvm", "qemu", "hyper-v", "xen"} {
		if strings.Contains(low, v) {
			isVirtual = true
			break
		}
	}

	info := &SysInfo{
		OS:          "windows",
		Model:       strings.TrimSpace(model),
		BIOSSerial:  strings.TrimSpace(biosSerial),
		UUID:        strings.TrimSpace(machineUUID),
		Serial:      strings.TrimSpace(boardSerial),
		IsVirtual:   isVirtual,
		DetectHints: map[string]string{"ManufacturerModel": model},
	}
	return info, nil
}
