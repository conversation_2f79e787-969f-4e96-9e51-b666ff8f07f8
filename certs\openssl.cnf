# Minimal openssl.cnf
# Only what we need for 'req' and a basic CA
[ req ]
default_bits       = 2048
distinguished_name = dn
prompt             = no
string_mask        = utf8only

[ dn ]
C  = US
ST = State
L  = City
O  = MyOrg
OU = Generated
CN = placeholder

# Extensions for a self-signed CA cert
[ v3_ca ]
basicConstraints = critical, CA:true, pathlen:0
keyUsage         = critical, keyCertSign, cRLSign
subjectKeyIdentifier = hash
authorityKeyIdentifier = keyid:always,issuer
