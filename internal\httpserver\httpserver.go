package httpserver

import (
	"crypto/tls"
	"crypto/x509"
	"encoding/base64"
	"encoding/json"
	"encoding/pem"
	"fmt"
	"log"
	"net/http"
	"strings"
	"time"

	"Golang/SystemSupport/internal/sign"
	"Golang/SystemSupport/internal/sysinfo"
)

type Payload struct {
	UUID      string `json:"uuid"`
	Ts        int64  `json:"ts"`
	Nonce     string `json:"nonce"`
	DataB64   string `json:"data_b64"`
	Alg       string `json:"alg"`
	Signature string `json:"signature"`
}

func Build<PERSON>andler(signer *sign.Signer) http.Handler {
	mux := http.NewServeMux()

	mux.HandleFunc("/health", func(w http.ResponseWriter, r *http.Request) {
		w.Write([]byte("OK\n"))
	})

	mux.HandleFunc("/info", func(w http.ResponseWriter, r *http.Request) {
		sys, err := sysinfo.CollectWindowsInfo()
		if err != nil {
			http.Error(w, "collect info error: "+err.Error(), 500)
			return
		}

		rawUUID := strings.TrimSpace(sys.UUID)
		combo := fmt.Sprintf("%s%s%s",
			strings.TrimSpace(sys.Serial),
			strings.TrimSpace(sys.Model),
			rawUUID,
		)

		if sys.DetectHints == nil {
			sys.DetectHints = map[string]string{}
		}
		sys.DetectHints["RawUUID"] = rawUUID
		sys.UUID = combo

		sysJSON, _ := json.Marshal(sys)
		dataB64 := base64.StdEncoding.EncodeToString(sysJSON)

		ts := time.Now().Unix()
		nonce := fmt.Sprintf("%d", time.Now().UnixNano())

		msg := []byte(fmt.Sprintf("%s|%d|%s|%s", sys.UUID, ts, nonce, dataB64))
		sigB64, err := signer.SignB64(msg)
		if err != nil {
			http.Error(w, "sign error: "+err.Error(), 500)
			return
		}

		resp := &Payload{
			UUID:      sys.UUID,
			Ts:        ts,
			Nonce:     nonce,
			DataB64:   dataB64,
			Alg:       "ed25519",
			Signature: sigB64,
		}
		writeJSON(w, resp, 200)
	})

	return logMW(mux)
}

func writeJSON(w http.ResponseWriter, v any, status int) {
	w.Header().Set("Content-Type", "application/json; charset=utf-8")
	w.WriteHeader(status)
	_ = json.NewEncoder(w).Encode(v)
}

type respRec struct {
	http.ResponseWriter
	status int
}

func (rr *respRec) WriteHeader(code int) { rr.status = code; rr.ResponseWriter.WriteHeader(code) }

func logMW(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		start := time.Now()
		rr := &respRec{ResponseWriter: w, status: 200}
		next.ServeHTTP(rr, r)
		log.Printf("%s %s %d %s", r.Method, r.URL.Path, rr.status, time.Since(start))
	})
}

// ===== TLS từ PEM nhúng (giữ như bản gốc) =====

const serverCertPEM = `-----BEGIN CERTIFICATE-----
MIICQTCCAeegAwIBAgIUM6jAOd+bxebp9UWJoM20eYYMsuMwCgYIKoZIzj0EAwIw
YDELMAkGA1UEBhMCVk4xDDAKBgNVBAgMA0hDTTEMMAoGA1UEBwwDSENNMRAwDgYD
VQQKDAdZb3VyT3JnMQ8wDQYDVQQLDAZTZXJ2ZXIxEjAQBgNVBAMMCWxvY2FsaG9z
dDAeFw0yNTA5MjUxMzAzMzdaFw0zMDA5MjQxMzAzMzdaMGAxCzAJBgNVBAYTAlZO
MQwwCgYDVQQIDANIQ00xDDAKBgNVBAcMA0hDTTEQMA4GA1UECgwHWW91ck9yZzEP
MA0GA1UECwwGU2VydmVyMRIwEAYDVQQDDAlsb2NhbGhvc3QwWTATBgcqhkjOPQIB
BggqhkjOPQMBBwNCAATt1ImV6Ua3TfgOdSnEkBJutwVYk92BoehQOKRVSwPc686s
t2WpDuvk5jbaYIV1jlR+8jzckGTS+/RA1Gna2zGFo38wfTAJBgNVHRMEAjAAMA4G
A1UdDwEB/wQEAwIFoDATBgNVHSUEDDAKBggrBgEFBQcDATAsBgNVHREEJTAjggls
b2NhbGhvc3SHBH8AAAGHEAAAAAAAAAAAAAAAAAAAAAEwHQYDVR0OBBYEFHqw3qYN
bSwMYs8gk7Uji6/ZzZoNMAoGCCqGSM49BAMCA0gAMEUCIQDJ3MiFBEfJOXYSlPNE
nsuUfp2Xa+nGvXBLUGWUpM8lyAIgNwJTm/rF8ckFS/mZ4dlgrG1atsVtB7Ng3yH8
SFAl5aU=
-----END CERTIFICATE-----`

const serverKeyPEM = `-----BEGIN EC PARAMETERS-----
BggqhkjOPQMBBw==
-----END EC PARAMETERS-----
**********************************************************************************************************************************************************************************************************************************`

func TLSConfigFromEmbedded() *tls.Config {
	cert, err := tls.X509KeyPair([]byte(serverCertPEM), []byte(serverKeyPEM))
	if err != nil {
		log.Fatalf("load embedded TLS keypair: %v", err)
	}
	return &tls.Config{
		Certificates: []tls.Certificate{cert},
		MinVersion:   tls.VersionTLS12,
		ClientAuth:   tls.NoClientCert,
	}
}

// (Tuỳ nhu cầu có thể thêm hàm parse x509 cert nếu bạn cần)
func parseCertInfo() (*x509.Certificate, error) {
	block, _ := pem.Decode([]byte(serverCertPEM))
	if block == nil {
		return nil, fmt.Errorf("bad cert pem")
	}
	return x509.ParseCertificate(block.Bytes)
}
