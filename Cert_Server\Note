#####################################################################################
#####################################################################################
#####################################################################################
1) Tạo server cert tự ký (CN=localhost, có SAN)

# --- 0) Tên file đầu ra (có thể đổi nếu muốn) ---
$keyPath   = ".\server.key"
$csrPath   = ".\server.csr"
$crtPath   = ".\server.crt"
$miniConf  = ".\_openssl_min.req.cnf"
$extConf   = ".\server-ext.cnf"
$pinOutput = ".\PIN_SPKI_SHA256.txt"

# --- 1) Tạo private key ECDSA P-256 ---
openssl ecparam -genkey -name prime256v1 -out $keyPath

# --- 2) Tạo file cấu hình req "tối giản" để OpenSSL không đòi openssl.cnf hệ thống ---
@"
distinguished_name = req_distinguished_name
[ req_distinguished_name ]
"@ | Set-Content -Path $miniConf -Encoding ascii

# --- 3) Tạo file extensions cho certificate (SAN + EKU + KU) ---
@"
basicConstraints=CA:FALSE
keyUsage=critical, digitalSignature, keyEncipherment
extendedKeyUsage=serverAuth
subjectAltName=@alt_names
[alt_names]
DNS.1=localhost
IP.1=127.0.0.1
IP.2=::1
"@ | Set-Content -Path $extConf -Encoding ascii

# --- 4) Tạo CSR (dùng -subj như lệnh gốc, + trỏ -config vào file tối giản) ---
openssl req -new -key $keyPath -out $csrPath `
  -subj "/C=VN/ST=HCM/L=HCM/O=YourOrg/OU=Server/CN=localhost" `
  -config $miniConf

# --- 5) Ký CSR thành self-signed cert 5 năm, áp dụng extensions ở trên ---
openssl x509 -req -in $csrPath -signkey $keyPath -out $crtPath `
  -days 1825 -sha256 -extfile $extConf

# --- 6) In SPKI pin (base64) để dùng cho Node (PIN_SPKI_SHA256) ---
Write-Host "`n=== SPKI pin (base64, SHA-256) ==="
$pin = (openssl x509 -in $crtPath -noout -pubkey `
       | openssl pkey -pubin -outform DER `
       | openssl dgst -sha256 -binary `
       | openssl base64 -A)
Write-Host $pin
Write-Host "==================================`n"

# --- 7) Lưu pin ra file ---
$pin | Set-Content -Path $pinOutput -NoNewline
Write-Host "SPKI pin đã được ghi vào $pinOutput"

# (Tuỳ chọn) Kiểm tra nhanh SAN
# openssl x509 -in $crtPath -noout -text | Select-String "Subject Alternative Name"








#############################################################################
#############################################################################
#############################################################################
#############################################################################
#############################################################################
2) Tạo cặp khoá Ed25519
# Private (PKCS#8)
openssl genpkey -algorithm ed25519 -out ed25519-private.pem
# Public
openssl pkey -in ed25519-private.pem -pubout -out ed25519-public.pem


Paste ed25519-private.pem vào ed25519PrivPEM (Go).
Paste ed25519-public.pem vào ED25519_PUB_PEM (Node).