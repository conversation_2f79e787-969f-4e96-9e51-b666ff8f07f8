package mqttclient

import (
	"context"
	"crypto/tls"
	"fmt"
	"log"
	"time"

	mqtt "github.com/eclipse/paho.mqtt.golang"

	"Golang/SystemSupport/internal/config"
)

type Handle struct {
	client mqtt.Client
}

func (h *Handle) Close() {
	if h.client != nil && h.client.IsConnectionOpen() {
		h.client.Disconnect(250)
	}
}

// Start khởi tạo MQTT client từ config. Nếu cfg == nil: trả nil, nil.
func Start(ctx context.Context, cfg *config.MQTT) (*Handle, error) {
	if cfg == nil || cfg.Host == "" || cfg.Port == 0 {
		return nil, nil
	}

	scheme := "tcp"
	var tlsCfg *tls.Config
	if cfg.TLS.Enable {
		scheme = "ssl"
		tlsCfg = &tls.Config{InsecureSkipVerify: cfg.TLS.InsecureSkipVerify}
	}

	brokerURL := fmt.Sprintf("%s://%s:%d", scheme, cfg.Host, cfg.Port)

	opts := mqtt.NewClientOptions().AddBroker(brokerURL)
	clientID := cfg.ClientID
	if clientID == "" {
		clientID = fmt.Sprintf("sysinfo-%d", time.Now().UnixNano())
	}
	opts.SetClientID(clientID)
	if cfg.Username != "" {
		opts.SetUsername(cfg.Username)
		opts.SetPassword(cfg.Password)
	}
	if tlsCfg != nil {
		opts.SetTLSConfig(tlsCfg)
	}
	opts.SetCleanSession(true)
	opts.SetAutoReconnect(true)
	opts.SetConnectTimeout(5 * time.Second)
	opts.SetKeepAlive(30 * time.Second)
	opts.SetOrderMatters(false)

	topic := cfg.topicSupport
	if topic == "" {
		topic = "topicSupport"
	}

	opts.OnConnect = func(c mqtt.Client) {
		log.Printf("MQTT connected -> %s (client_id=%s)", brokerURL, clientID)
		// publish lời chào khi connect
		topic = "SystemSupportStatus"
		payload := "Hello from MQTT client"
		tok := c.Publish(topic, 1, false, payload)
		if ok := tok.WaitTimeout(5 * time.Second); !ok {
			log.Printf("MQTT publish timeout on topic %q", topic)
		}
		if err := tok.Error(); err != nil {
			log.Printf("MQTT publish error: %v", err)
		} else {
			log.Printf("MQTT published %q to %q", payload, topic)
		}
	}

	opts.OnConnectionLost = func(c mqtt.Client, err error) {
		log.Printf("MQTT connection lost: %v", err)
	}

	client := mqtt.NewClient(opts)
	tok := client.Connect()
	if ok := tok.WaitTimeout(10 * time.Second); !ok {
		return nil, fmt.Errorf("mqtt connect timeout to %s", brokerURL)
	}
	if err := tok.Error(); err != nil {
		return nil, fmt.Errorf("mqtt connect error: %w", err)
	}

	h := &Handle{client: client}

	// đóng khi ctx hủy
	go func() {
		<-ctx.Done()
		h.Close()
	}()

	return h, nil
}
