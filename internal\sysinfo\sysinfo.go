package sysinfo

import (
	"bytes"
	"fmt"
	"os/exec"
	"strings"
)

type SysInfo struct {
	OS          string            `json:"os"`
	Model       string            `json:"model"`
	BIOSSerial  string            `json:"bios_serial"`
	UUID        string            `json:"uuid"`
	Serial      string            `json:"serial"`
	IsVirtual   bool              `json:"is_virtual"`
	DetectHints map[string]string `json:"detect_hints,omitempty"`
}

// helper: run PowerShell and capture stdout/stderr
func runPowershell(expr string) (string, error) {
	cmd := exec.Command("powershell", "-NoProfile", "-Command", expr)
	var buf bytes.Buffer
	cmd.Stdout, cmd.Stderr = &buf, &buf
	err := cmd.Run()
	out := strings.ReplaceAll(buf.String(), "\r\n", "\n")
	return strings.TrimSpace(out), err
}

func CollectWindowsInfo() (*SysInfo, error) {
	model, err1 := runPowershell("(Get-CimInstance Win32_ComputerSystem).Manufacturer + ' ' + (Get-CimInstance Win32_ComputerSystem).Model")
	biosSerial, err2 := runPowershell("(Get-CimInstance Win32_BIOS).SerialNumber")
	machineUUID, err3 := runPowershell("(Get-CimInstance Win32_ComputerSystemProduct).UUID")
	boardSerial, err4 := runPowershell("(Get-CimInstance Win32_BaseBoard).SerialNumber")

	if err1 != nil && err2 != nil && err3 != nil && err4 != nil {
		return nil, fmt.Errorf("cannot collect any wmi info")
	}

	low := strings.ToLower(model)
	isVirtual := strings.Contains(low, "vmware") || strings.Contains(low, "virtualbox") ||
		strings.Contains(low, "kvm") || strings.Contains(low, "qemu") ||
		strings.Contains(low, "hyper-v") || strings.Contains(low, "xen")

	return &SysInfo{
		OS:          "windows",
		Model:       strings.TrimSpace(model),
		BIOSSerial:  strings.TrimSpace(biosSerial),
		UUID:        strings.TrimSpace(machineUUID),
		Serial:      strings.TrimSpace(boardSerial),
		IsVirtual:   isVirtual,
		DetectHints: map[string]string{"ManufacturerModel": model},
	}, nil
}
