# generate-certs.ps1
$ErrorActionPreference = "Stop"

function Run-OpenSsl {
  param(
    [Parameter(ValueFromRemainingArguments = $true)]
    [string[]] $Args
  )
  Write-Host ">> openssl $($Args -join ' ')"
  & openssl @Args
  if ($LASTEXITCODE -ne 0) {
    throw "OpenSSL command failed: $($Args -join ' ')"
  }
}

# 0) Kiểm tra openssl
if (-not (Get-Command openssl -ErrorAction SilentlyContinue)) {
  throw "Không tìm thấy 'openssl'. Cài nhanh:  choco install openssl -y"
}

# 1) <PERSON><PERSON>n bị thư mục
Remove-Item -Recurse -Force certs -ErrorAction SilentlyContinue
New-Item -ItemType Directory certs | Out-Null

# 2) Tìm hoặc tạo file cấu hình OpenSSL
$candidates = @(
  "$env:ProgramFiles\OpenSSL-Win64\bin\openssl.cfg",
  "$env:ProgramFiles\OpenSSL-Win64\bin\openssl.cnf",
  "$env:ProgramFiles\Git\usr\ssl\openssl.cnf"
) | Where-Object { Test-Path $_ }

if ($candidates.Count -gt 0) {
  $env:OPENSSL_CONF = $candidates[0]
  Write-Host "OPENSSL_CONF = $env:OPENSSL_CONF"
} else {
  $mini = @"
# Minimal openssl.cnf for req
[ req ]
prompt = no
distinguished_name = req_dn

[ req_dn ]
CN = temp
"@
  $miniPath = Join-Path (Get-Location) "certs\openssl.cnf"
  Set-Content -Path $miniPath -Value $mini -Encoding ASCII
  $env:OPENSSL_CONF = $miniPath
  Write-Host "OPENSSL_CONF (mini) = $env:OPENSSL_CONF"
}

# 3) CA (ca.key + ca.crt)
Run-OpenSsl genpkey -algorithm RSA -out certs\ca.key -pkeyopt rsa_keygen_bits:4096
Run-OpenSsl req -config "$env:OPENSSL_CONF" -x509 -new -nodes -key certs\ca.key -sha256 -days 3650 -subj "/CN=MyLocalCA" -out certs\ca.crt

# 4) Client cert cho host (host.key + host.crt)
Run-OpenSsl genpkey -algorithm EC -out certs\host.key -pkeyopt ec_paramgen_curve:prime256v1
Run-OpenSsl req -config "$env:OPENSSL_CONF" -new -key certs\host.key -subj "/CN=windows-host-sender" -out certs\host.csr
Run-OpenSsl x509 -req -in certs\host.csr -CA certs\ca.crt -CAkey certs\ca.key -CAcreateserial -out certs\host.crt -days 365 -sha256

# 5) Server cert cho Node (server.key + server.crt) với SAN localhost
Run-OpenSsl genpkey -algorithm RSA -out certs\server.key -pkeyopt rsa_keygen_bits:2048
Run-OpenSsl req -config "$env:OPENSSL_CONF" -new -key certs\server.key -subj "/CN=container-server" -out certs\server.csr
@"
subjectAltName = DNS:localhost,IP:127.0.0.1
"@ | Set-Content certs\server.ext -Encoding ASCII
Run-OpenSsl x509 -req -in certs\server.csr -CA certs\ca.crt -CAkey certs\ca.key -CAcreateserial -out certs\server.crt -days 365 -sha256 -extfile certs\server.ext

# 6) Sinh HMAC secret ngẫu nhiên (32 bytes hex)
$bytes = New-Object 'byte[]' 32
[System.Security.Cryptography.RandomNumberGenerator]::Fill($bytes)
$hex = ([System.BitConverter]::ToString($bytes)).Replace('-','').ToLower()
Set-Content -Path certs\hmac_secret.txt -Value $hex -Encoding ASCII

Write-Host "`n==> ĐÃ TẠO trong certs\ :"
Get-ChildItem certs\ | Select Name,Length,LastWriteTime

Write-Host "`nKiểm tra host.crt:"
openssl x509 -in certs\host.crt -noout -subject -issuer
Write-Host "`nKiểm tra server.crt:"
openssl x509 -in certs\server.crt -noout -subject -issuer
