package main

import (
	"context"
	"crypto/tls"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"Golang/SystemSupport/internal/config"
	"Golang/SystemSupport/internal/httpserver"
	"Golang/SystemSupport/internal/mqttclient"
	"Golang/SystemSupport/internal/sign"
)

func main() {
	// 1) Load config (MQTT, ...)
	cfg, err := config.Load("config.json")
	if err != nil {
		log.Printf("config load warning: %v (MQTT sẽ tắt nếu thiếu)", err)
	}

	// 2) Init signer ED25519 (PEM nhúng như bản gốc)
	signer, err := sign.NewEd25519SignerFromPEM(sign.DefaultEd25519PrivPEM)
	if err != nil {
		log.Fatalf("init signer error: %v", err)
	}

	// 3) Build HTTP handler (giữ nguyên logic /health, /info)
	handler := httpserver.BuildHandler(signer)

	// 4) TLS config từ cert/key PEM nhúng (nh<PERSON> bản gốc)
	tlsCfg := httpserver.TLSConfigFromEmbedded()

	// 5) Context + graceful shutdown
	ctx, stop := signal.NotifyContext(context.Background(),
		os.Interrupt, syscall.SIGTERM)
	defer stop()

	// 6) Start MQTT nếu có cấu hình
	mqttHandle, err := mqttclient.Start(ctx, cfg.MQTT)
	if err != nil {
		log.Fatalf("mqtt start error: %v", err)
	}
	if mqttHandle == nil {
		log.Printf("MQTT: disabled or not configured")
	}

	// 7) Start HTTPS trên 127.0.0.1:8443 (như bản gốc)
	addr := "127.0.0.1:8443"
	ln, err := tls.Listen("tcp", addr, tlsCfg)
	if err != nil {
		log.Fatalf("listen tls: %v", err)
	}
	srv := &http.Server{Handler: handler, TLSConfig: tlsCfg}

	log.Printf("Go server on https://%s (embedded TLS, ed25519 signing)", addr)

	go func() {
		if err := srv.Serve(ln); err != nil && err != http.ErrServerClosed {
			log.Fatalf("http serve: %v", err)
		}
	}()

	// 8) Đợi tín hiệu tắt
	<-ctx.Done()
	log.Printf("shutting down...")

	// 9) Shutdown HTTP
	shutdownCtx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()
	_ = srv.Shutdown(shutdownCtx)

	// 10) MQTT sẽ đóng do context hủy; nhưng gọi Close cho chắc
	if mqttHandle != nil {
		mqttHandle.Close()
	}

	log.Printf("bye.")
}
